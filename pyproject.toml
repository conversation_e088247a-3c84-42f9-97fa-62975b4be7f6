[project]
name = "OpenDeepSearch"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "Salaheddin Alzu'bi", email = "salahedd<PERSON><PERSON><PERSON>@gmail.com"},
]

dependencies = ["openai>=1.66.2", "datasets>=3.3.2", "transformers>=4.49.0", "litellm>=1.61.20", "langchain>=0.3.19", "crawl4ai @ git+https://github.com/salzubi401/crawl4ai.git@main", "fasttext-wheel>=0.9.2", "wikipedia-api>=0.8.1", "pillow>=10.4.0", "smolagents>=1.9.2", "gradio==5.20.1", "torch>=2.0.0", "loguru>=0.7.0", "vllm>=0.6.0", "nest-asyncio>=1.5.0", "pydantic==2.10.6"]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.pdm]
distribution = true

[tool.hatch.metadata]
allow-direct-references = true

[tool.uv]
