#!/usr/bin/env python3
"""
OpenDeepSearch 直接调用示例
基于README中的standalone示例，支持交互式问答
"""

from opendeepsearch import OpenDeepSearchTool
import os
from dotenv import load_dotenv

def main():
    # 加载环境变量
    load_dotenv()
    
    print("🔍 OpenDeepSearch 直接调用")
    print("=" * 40)
    
    # 初始化搜索工具
    search_agent = OpenDeepSearchTool(
        model_name=os.getenv("LITELLM_SEARCH_MODEL_ID", os.getenv("LITELLM_MODEL_ID", "openrouter/google/gemini-2.0-flash-001")),
        reranker="jina",
        search_provider="searxng"  # 或者使用 "serper"
    )
    
    # 确保工具已初始化
    if not search_agent.is_initialized:
        search_agent.setup()
    
    print("✅ 搜索工具初始化完成")
    print("💡 输入 'quit' 或 'exit' 退出")
    print("-" * 40)
    
    # 交互循环
    while True:
        try:
            # 获取用户输入
            query = input("\n🤔 请输入您的问题: ").strip()
            
            # 检查退出命令
            if query.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见!")
                break
            
            # 检查空输入
            if not query:
                continue
            
            print("⏳ 搜索中...")
            
            # 执行搜索
            result = search_agent.forward(query)
            
            # 显示结果
            print("\n📋 搜索结果:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    main()
