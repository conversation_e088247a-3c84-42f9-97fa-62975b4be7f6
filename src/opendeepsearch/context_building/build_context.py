from typing import List, Dict, Optional
from loguru import logger
from langchain.text_splitter import RecursiveCharacterTextSplitter


def extract_information(organic_results: List[Dict]) -> List[str]:
    """Extract snippets from organic search results in a formatted string."""
    formatted_results = []
    for item in organic_results:
        if 'snippet' in item:
            result_parts = [
                f"title: {item.get('title', 'N/A')}",
                f"date authored: {item.get('date', 'N/A')}",
                f"link: {item.get('link', 'N/A')}",
                f"snippet: {item['snippet']}"
            ]
            
            if 'html' in item:
                result_parts.append(f"additional information: {item['html']}")
            
            formatted_results.append('\n'.join(result_parts))
    
    return formatted_results

def extract_top_stories(top_stories: Optional[List[Dict]]) -> List[str]:
    """Extract titles from top stories."""
    if not top_stories:
        return []
    
    return [
        item['title'] 
        for item in top_stories 
        if 'title' in item
    ]

def extract_answer_box(
    answer_box: Optional[Dict]
) -> List[str]:
    """Extract information from answer box."""
    results = []
    
    if answer_box:
        for key in ['answer', 'snippet']:
            if answer_box.get(key):
                results.append(answer_box[key])
    
    return results

def build_context(
    sources_result,
) -> str:
    """
    Build context from search results.

    Args:
        sources_result: Dictionary containing search results or SearchResult object

    Returns:
        A formatted string containing all relevant search results
    """
    try:
        # Handle both SearchResult objects and dictionaries
        if hasattr(sources_result, 'data'):
            # It's a SearchResult object
            if sources_result.failed or sources_result.data is None:
                logger.warning(f"Search failed: {getattr(sources_result, 'error', 'Unknown error')}")
                return ""
            search_data = sources_result.data
        elif isinstance(sources_result, dict):
            # It's already a dictionary
            if not sources_result:  # Empty dictionary
                logger.warning("Received empty search data")
                return ""
            search_data = sources_result
        elif sources_result is None:
            logger.warning("Received None as sources_result")
            return ""
        else:
            logger.error(f"Unexpected sources_result type: {type(sources_result)}")
            return ""

        # Build context from different components
        organic_results = extract_information(search_data.get('organic', []))
        top_stories = extract_top_stories(search_data.get('topStories'))
        answer_box = extract_answer_box(
            search_data.get('answerBox')
        )
        
        # Combine all results into a single string
        context_parts = []
        
        # Add answer box if available
        if answer_box:
            context_parts.append("ANSWER BOX:")
            context_parts.extend(answer_box)
            context_parts.append("")  # Empty line for separation
        
        # Add organic results
        if organic_results:
            context_parts.append("SEARCH RESULTS:")
            context_parts.extend(organic_results)
            context_parts.append("")  # Empty line for separation
        
        # Add top stories if available
        if top_stories:
            context_parts.append("TOP STORIES:")
            context_parts.extend(top_stories)
        
        # Join all parts with newlines
        return "\n".join(context_parts)

    except Exception as e:
        logger.exception(f"An error occurred while building context: {e}")
        return ""  # Return empty string in case of error
