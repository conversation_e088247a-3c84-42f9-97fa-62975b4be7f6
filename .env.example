# SEARXNG_INSTANCE_URL=http://searxng:8080
# or
# SERPER_API_KEY=

JINA_API_KEY=
WOLFRAM_ALPHA_APP_ID=

### Providers ###
OPENAI_API_KEY=
OPENAI_BASE_URL=
ANTHROPIC_API_KEY=
OPENROUTER_API_KEY=

# LiteLLM model IDs for different tasks
LITELLM_MODEL_ID=openrouter/google/gemini-2.0-flash-001
LITELLM_SEARCH_MODEL_ID=openrouter/google/gemini-2.0-flash-001
LITELLM_ORCHESTRATOR_MODEL_ID=openrouter/google/gemini-2.0-flash-001
LITELLM_EVAL_MODEL_ID=gpt-4o-mini
FIREWORKS_API_KEY=
